worker_processes 1;

events { worker_connections 1024; }

http {
  include       mime.types;
  default_type  application/octet-stream;
  sendfile        on;

  server {
    listen 8080;

    location /api {
      proxy_pass http://127.0.0.1:8001;
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection keep-alive;
      proxy_set_header Host $host;
      proxy_cache_bypass $http_upgrade;
    }

    location / {
      root /usr/share/nginx/html;
      index index.html index.htm;
      try_files $uri /index.html;
    }
  }
}