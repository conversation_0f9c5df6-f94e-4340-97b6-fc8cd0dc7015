/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* Modern CSS Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--hero-bg);
  overflow-x: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom Properties for Design System */
:root {
  --primary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
  --secondary-gradient: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
  --surface-gradient: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 0 1px rgba(249, 115, 22, 0.1), 0 8px 32px rgba(249, 115, 22, 0.2);
  --border-radius: 16px;
  --border-radius-large: 24px;

  /* Light mode colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1f2937;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --border-color: rgba(0, 0, 0, 0.1);
  --nav-bg: rgba(255, 255, 255, 0.9);
  --card-bg: var(--surface-gradient);
  --hero-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scrolling and improved scrollbar */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 4rem;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: linear-gradient(to bottom, #f1f5f9, #e2e8f0);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-gradient);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

h1 {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

p {
  font-weight: 400;
  line-height: 1.7;
  color: var(--text-secondary);
}

/* Modern Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  position: relative;
  overflow: hidden;
  transform: translateY(0);
  user-select: none;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s;
}

.btn:hover::before {
  left: 100%;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:active::after {
  width: 300px;
  height: 300px;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-glow);
  position: relative;
}

.btn-primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 0 0 1px rgba(249, 115, 22, 0.2), 0 25px 50px rgba(249, 115, 22, 0.4);
}

.btn-primary:active {
  transform: translateY(-1px) scale(0.98);
}

.btn-secondary {
  background: var(--glass-bg);
  color: white;
  border: 2px solid var(--glass-border);
  backdrop-filter: blur(20px);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-large);
  border-color: rgba(255, 255, 255, 0.4);
}

.btn-outline {
  background: transparent;
  color: #f97316;
  border: 2px solid #f97316;
  position: relative;
}

.btn-outline::before {
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s;
}

.btn-outline:hover {
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-glow);
}

.btn-outline:hover::before {
  opacity: 1;
}

/* New button variants */
.btn-ghost {
  background: transparent;
  color: var(--text-primary);
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
}

.btn-ghost:hover {
  background: rgba(249, 115, 22, 0.1);
  border-color: rgba(249, 115, 22, 0.3);
  transform: translateY(-2px);
}

.btn-gradient {
  background: linear-gradient(45deg, #f97316, #ea580c, #dc2626, #b91c1c);
  background-size: 300% 300%;
  animation: gradientShift 3s ease infinite;
  color: white;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Modern Card Designs */
.card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.5), transparent);
}

.card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-large);
  border-color: rgba(249, 115, 22, 0.3);
}

.card:hover::after {
  opacity: 1;
}

.card-feature {
  background: linear-gradient(135deg, #ffffff 0%, #fef7ff 50%, #fff7ed 100%);
  border: 1px solid rgba(249, 115, 22, 0.1);
  position: relative;
}

.card-feature::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(249, 115, 22, 0.1), transparent);
  opacity: 0;
  transition: opacity 0.5s ease;
  animation: rotate 8s linear infinite;
}

.card-feature:hover {
  background: linear-gradient(135deg, #ffffff 0%, #fef3c7 50%, #fed7aa 100%);
  border-color: rgba(249, 115, 22, 0.4);
  transform: translateY(-8px) scale(1.02);
}

.card-feature:hover::before {
  opacity: 1;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Enhanced card variants */
.card-interactive {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-interactive:hover {
  transform: translateY(-8px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-neon {
  border: 2px solid transparent;
  background: linear-gradient(var(--card-bg), var(--card-bg)) padding-box,
              linear-gradient(45deg, #f97316, #ea580c, #dc2626) border-box;
}

/* Glass Morphism Effects */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-medium);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(30px) saturate(200%);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Advanced Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.2);
  }
  50% {
    box-shadow: 0 0 40px rgba(249, 115, 22, 0.4);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-200deg);
  }
  to {
    opacity: 1;
    transform: rotate(0deg);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes morphing {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
}

/* Animation Classes */
.animate-fadeInUp {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-fadeInRight {
  animation: fadeInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scaleIn {
  animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-slideInFromBottom {
  animation: slideInFromBottom 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-bounceIn {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-rotateIn {
  animation: rotateIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slideInFromLeft {
  animation: slideInFromLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slideInFromRight {
  animation: slideInFromRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-morphing {
  animation: morphing 8s ease-in-out infinite;
}

.animate-slideInFromLeft {
  animation: slideInFromLeft 0.6s ease-out forwards;
}

.animate-fadeInScale {
  animation: fadeInScale 0.4s ease-out forwards;
}

/* Delayed animations for staggered effects */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-800 { animation-delay: 0.8s; }

/* Modern Hero Section */
.hero-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  overflow: hidden;
}

.hero-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
  opacity: 0.3;
}

/* Enhanced Navigation */
.nav-glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  will-change: background-color, backdrop-filter;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Mobile Navigation Enhancements */
.nav-mobile {
  will-change: transform, opacity;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.nav-item {
  will-change: transform, background-color;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-item:hover::before {
  left: 100%;
}

/* Enhanced Focus Indicators */
.focus-ring {
  @apply focus:outline-none focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2 focus-visible:ring-offset-white dark:focus-visible:ring-offset-gray-800;
}

/* Better Hover Effects */
.hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

/* Micro-interactions */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
  width: 300px;
  height: 300px;
}

/* Enhanced Button States */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-enhanced:focus {
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.3);
}

.btn-enhanced:active {
  transform: translateY(0);
}

/* Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-slideInFromLeft,
  .animate-fadeInScale,
  .animate-pulse,
  .animate-bounce {
    animation: none !important;
  }
}

/* Optimize for high refresh rate displays */
@media (min-resolution: 120dpi) {
  .smooth-animation {
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --primary-gradient: linear-gradient(135deg, #000000 0%, #333333 100%);
    --text-primary: #000000;
    --text-secondary: #000000;
    --border-color: #000000;
    --bg-primary: #ffffff;
  }

  .dark {
    --primary-gradient: linear-gradient(135deg, #ffffff 0%, #cccccc 100%);
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --border-color: #ffffff;
    --bg-primary: #000000;
  }

  .nav-glass {
    background: var(--bg-primary) !important;
    border: 2px solid var(--border-color) !important;
    backdrop-filter: none !important;
  }

  .btn {
    border: 2px solid var(--border-color) !important;
  }

  .card {
    border: 2px solid var(--border-color) !important;
    background: var(--bg-primary) !important;
  }
}

/* Focus indicators for better accessibility */
.focus-visible {
  outline: 3px solid #f97316 !important;
  outline-offset: 2px !important;
}

/* Screen reader only content */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Modern Table Styles */
.modern-table {
  background: white;
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  border: 1px solid rgba(249, 115, 22, 0.1);
}

.modern-table th {
  background: var(--primary-gradient);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.875rem;
}

.modern-table tr:nth-child(even) {
  background: linear-gradient(135deg, #fafafa 0%, #f8fafc 100%);
}

.modern-table tr:hover {
  background: linear-gradient(135deg, #fef7ff 0%, #fff7ed 100%);
  transform: scale(1.01);
  transition: var(--transition);
}

/* FAQ Enhancements */
.faq-item {
  background: white;
  border-radius: var(--border-radius);
  border: 1px solid rgba(249, 115, 22, 0.1);
  transition: var(--transition);
  overflow: hidden;
}

.faq-item:hover {
  border-color: rgba(249, 115, 22, 0.3);
  box-shadow: var(--shadow-medium);
}

.faq-button {
  transition: var(--transition);
}

.faq-button:hover {
  background: linear-gradient(135deg, #fef7ff 0%, #fff7ed 100%);
}

/* Enhanced Form Elements */
input, textarea, select {
  border-radius: var(--border-radius);
  border: 2px solid #e2e8f0;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  transition: var(--transition);
  background: white;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

/* Statistics Counter Animation */
.stat-number {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 900;
}

/* Enhanced Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.badge-info {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

/* Tablet Optimizations */
@media (min-width: 768px) and (max-width: 1023px) {
  .nav-glass {
    padding: 0 1rem;
  }

  /* Condensed navigation for tablets */
  .desktop-nav-condensed {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .desktop-nav-condensed .nav-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .hero-bg h1 {
    font-size: 3rem;
    line-height: 1.1;
  }

  .hero-bg p {
    font-size: 1.125rem;
    max-width: 600px;
  }

  .card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Enhanced Mobile Optimizations */
@media (max-width: 767px) {
  .hero-bg h1 {
    font-size: 2rem;
    line-height: 1.1;
  }

  .hero-bg p {
    font-size: 1rem;
  }

  .btn {
    width: 100%;
    justify-content: center;
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }

  .card {
    margin-bottom: 1rem;
    padding: 1rem;
  }

  .grid-responsive {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .nav-mobile {
    padding: 0.5rem;
  }

  .text-responsive {
    font-size: 1.25rem;
  }
}

/* Small Mobile Optimizations */
@media (max-width: 480px) {
  .hero-bg h1 {
    font-size: 1.75rem;
  }

  .hero-bg p {
    font-size: 0.9rem;
  }

  .nav-mobile {
    margin: 0.25rem;
    padding: 0.75rem;
  }

  .nav-item {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .btn {
    width: 100%;
    justify-content: center;
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 768px) {
  .hero-bg h1 {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .hero-bg p {
    font-size: 1.125rem;
  }

  .card {
    margin-bottom: 1rem;
    padding: 1.5rem;
  }

  .btn {
    width: 100%;
    justify-content: center;
    padding: 1rem 2rem;
  }

  .grid-responsive {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .comparison-table {
    font-size: 0.875rem;
  }

  .comparison-table th,
  .comparison-table td {
    padding: 0.75rem 0.5rem;
  }

  .faq-item {
    margin-bottom: 1rem;
  }

  .text-responsive {
    font-size: 1.5rem;
  }

  /* Touch-friendly interactions */
  .btn, .card, .faq-button {
    min-height: 44px;
  }

  .nav-item {
    min-height: 48px;
    padding: 0.75rem 1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
  }

  .hero-bg h1 {
    font-size: 3.5rem;
  }

  .text-responsive {
    font-size: 1.75rem;
  }
}

@media (min-width: 1025px) {
  .grid-responsive {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }

  .text-responsive {
    font-size: 2rem;
  }
}

/* Enhanced touch interactions */
@media (hover: none) and (pointer: coarse) {
  .hover-lift:hover {
    transform: none;
  }

  .hover-lift:active {
    transform: translateY(-2px) scale(0.98);
  }

  .hover-glow:hover {
    transform: none;
    box-shadow: none;
  }

  .hover-glow:active {
    transform: scale(0.98);
    box-shadow: 0 0 15px rgba(249, 115, 22, 0.3);
  }

  .card:hover {
    transform: none;
  }

  .card:active {
    transform: translateY(-4px) scale(0.98);
  }

  .btn:hover {
    transform: none;
  }

  .btn:active {
    transform: scale(0.95);
  }
}

/* Improved accessibility for mobile */
@media (max-width: 768px) {
  .sr-only-mobile {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus-visible:focus {
    outline: 3px solid #f97316;
    outline-offset: 2px;
  }
}

/* Dark Mode Variables */
.dark-mode,
.dark {
  --primary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
  --secondary-gradient: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
  --surface-gradient: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  --glass-bg: rgba(0, 0, 0, 0.3);
  --glass-border: rgba(255, 255, 255, 0.1);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-large: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 0 1px rgba(249, 115, 22, 0.2), 0 8px 32px rgba(249, 115, 22, 0.3);

  /* Dark mode colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-color: rgba(255, 255, 255, 0.1);
  --nav-bg: rgba(15, 23, 42, 0.9);
  --card-bg: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --hero-bg: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.dark-mode body,
.dark body {
  background: var(--hero-bg);
  color: var(--text-primary);
}

/* Dark mode specific adjustments */
.dark-mode .nav-glass,
.dark .nav-glass {
  background: var(--nav-bg);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
}

.dark-mode .card-feature,
.dark .card-feature {
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  border: 1px solid rgba(249, 115, 22, 0.2);
}

.dark-mode .modern-table,
.dark .modern-table {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.dark-mode .modern-table tr:nth-child(even),
.dark .modern-table tr:nth-child(even) {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
}

.dark-mode .modern-table tr:hover,
.dark .modern-table tr:hover {
  background: linear-gradient(135deg, #475569 0%, #64748b 100%);
}

/* Dark mode text adjustments */
.dark-mode h1,
.dark h1,
.dark-mode h2,
.dark h2,
.dark-mode h3,
.dark h3 {
  color: var(--text-primary);
}

/* Dark mode button adjustments */
.dark-mode .btn-secondary,
.dark .btn-secondary {
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  color: var(--text-primary);
}

.dark-mode .btn-outline,
.dark .btn-outline {
  color: #f97316;
  border-color: #f97316;
}

/* Loading States */
.loading {
  opacity: 0.7;
  pointer-events: none;
  cursor: not-allowed;
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced Focus and accessibility improvements */
*:focus {
  outline: 3px solid #f97316;
  outline-offset: 2px;
  border-radius: 4px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 3px solid #f97316;
  outline-offset: 2px;
  border-radius: 4px;
}

/* High contrast focus for better accessibility */
@media (prefers-contrast: high) {
  *:focus,
  *:focus-visible {
    outline: 4px solid #000;
    outline-offset: 2px;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #f97316;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: bold;
}

.skip-link:focus {
  top: 6px;
}

/* Enhanced button accessibility */
.btn:focus-visible {
  outline: 3px solid #fff;
  outline-offset: 2px;
  box-shadow: 0 0 0 6px rgba(249, 115, 22, 0.5);
}

/* Improved form accessibility */
input:focus,
textarea:focus,
select:focus {
  outline: 3px solid #f97316;
  outline-offset: 2px;
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border: 2px solid;
  }

  .btn {
    border: 2px solid;
  }

  .text-gradient,
  .text-gradient-animated {
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    color: inherit;
  }
}

/* Screen reader improvements */
.sr-only-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Keyboard navigation indicators */
.keyboard-nav *:focus {
  outline: 3px solid #f97316;
  outline-offset: 2px;
}

/* Improved color contrast for dark mode */
.dark {
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
}

/* ARIA live region styling */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Focus trap styling */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Custom utility classes */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-animated {
  background: linear-gradient(45deg, #f97316, #ea580c, #dc2626, #b91c1c, #f97316);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease infinite;
}

.bg-pattern {
  background-image: radial-gradient(circle at 2px 2px, rgba(249, 115, 22, 0.1) 1px, transparent 0);
  background-size: 20px 20px;
}

.bg-pattern-animated {
  background-image: radial-gradient(circle at 2px 2px, rgba(249, 115, 22, 0.1) 1px, transparent 0);
  background-size: 20px 20px;
  animation: patternMove 20s linear infinite;
}

@keyframes patternMove {
  0% { background-position: 0 0; }
  100% { background-position: 20px 20px; }
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              var(--primary-gradient) border-box;
}

.border-gradient-animated {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(45deg, #f97316, #ea580c, #dc2626, #b91c1c, #f97316) border-box;
  background-size: 300% 300%;
  animation: gradientShift 3s ease infinite;
}

/* Interactive hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(249, 115, 22, 0.4);
  transform: scale(1.02);
}

.hover-rotate {
  transition: transform 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Gradient backgrounds */
.bg-gradient-radial {
  background: radial-gradient(circle at center, var(--primary-gradient));
}

.bg-gradient-conic {
  background: conic-gradient(from 0deg, #f97316, #ea580c, #dc2626, #b91c1c, #f97316);
}

.bg-mesh {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  background-size: 400% 400%;
  animation: meshMove 15s ease infinite;
}

@keyframes meshMove {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}